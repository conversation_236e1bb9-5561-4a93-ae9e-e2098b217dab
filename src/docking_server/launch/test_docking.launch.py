#!/usr/bin/env python3
"""
简化的Docking Server测试启动文件
用于测试激光雷达线段匹配充电桩检测功能

作者: Augment Agent
日期: 2025-07-15
"""

import os

from ament_index_python.packages import get_package_share_directory

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    """
    生成测试launch描述
    """
    
    # 获取包路径
    docking_server_pkg_dir = get_package_share_directory('docking_server')
    
    # 声明launch参数
    use_sim_time = LaunchConfiguration('use_sim_time')
    params_file = LaunchConfiguration('params_file')
    
    # 参数文件路径
    default_params_file = os.path.join(docking_server_pkg_dir, 'config', 'docking_params.yaml')
    
    # 声明launch参数
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='使用仿真时间')
        
    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=default_params_file,
        description='参数文件的完整路径')
    
    # 激光线段匹配充电桩检测器节点
    laser_line_dock_detector_cmd = Node(
        package='docking_server',
        executable='laser_line_dock_detector',
        name='laser_line_dock_detector',
        output='screen',
        parameters=[params_file],
        remappings=[
            ('/tf', 'tf'),
            ('/tf_static', 'tf_static'),
            ('/scan', '/scan')
        ])
    
    # 生命周期管理器
    lifecycle_manager_cmd = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_docking',
        output='screen',
        parameters=[
            {'use_sim_time': use_sim_time},
            {'autostart': True},
            {'node_names': ['laser_line_dock_detector']}
        ])
    
    # 启动信息
    start_info = LogInfo(
        msg=[
            '\n',
            '========================================\n',
            '  激光雷达线段匹配充电桩检测器测试\n',
            '========================================\n',
            '功能说明:\n',
            '- 基于激光雷达线段匹配的充电桩检测\n',
            '- 峰谷特征识别算法\n',
            '- 实时可视化\n',
            '\n',
            '服务接口:\n',
            '- /detect_dock: 检测充电桩位置\n',
            '\n',
            '可视化:\n',
            '- /dock_markers: 充电桩和线段可视化\n',
            '========================================\n'
        ]
    )
    
    # 创建launch描述
    ld = LaunchDescription()
    
    # 添加声明参数的动作
    ld.add_action(declare_use_sim_time_cmd)
    ld.add_action(declare_params_file_cmd)
    
    # 添加启动信息
    ld.add_action(start_info)
    
    # 添加节点
    ld.add_action(laser_line_dock_detector_cmd)
    ld.add_action(lifecycle_manager_cmd)
    
    return ld
