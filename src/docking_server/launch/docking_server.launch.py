#!/usr/bin/env python3
"""
RobotCar Docking Server Launch File
基于激光雷达线段匹配的充电桩对接功能启动文件

功能:
- 启动Nav2 Docking Server
- 配置激光雷达线段匹配检测器
- 设置充电桩对接控制器
- 管理生命周期

作者: Augment Agent
日期: 2025-07-15
"""

import os

from ament_index_python.packages import get_package_share_directory

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, GroupAction
from launch.conditions import IfCondition, UnlessCondition
from launch.substitutions import LaunchConfiguration, PythonExpression
from launch.substitutions import PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from nav2_common.launch import RewrittenYaml


def generate_launch_description():
    """
    生成docking server的launch描述
    """
    
    # 获取包路径
    docking_server_pkg_dir = get_package_share_directory('docking_server')
    nav2_pkg_dir = get_package_share_directory('nav2_bringup')
    
    # 声明launch参数
    namespace = LaunchConfiguration('namespace')
    use_sim_time = LaunchConfiguration('use_sim_time')
    autostart = LaunchConfiguration('autostart')
    params_file = LaunchConfiguration('params_file')
    use_composition = LaunchConfiguration('use_composition')
    container_name = LaunchConfiguration('container_name')
    use_respawn = LaunchConfiguration('use_respawn')
    log_level = LaunchConfiguration('log_level')
    
    # 参数文件路径
    default_params_file = os.path.join(docking_server_pkg_dir, 'config', 'docking_params.yaml')
    
    # 声明launch参数
    declare_namespace_cmd = DeclareLaunchArgument(
        'namespace',
        default_value='',
        description='顶层命名空间')
        
    declare_use_sim_time_cmd = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='使用仿真时间')
        
    declare_params_file_cmd = DeclareLaunchArgument(
        'params_file',
        default_value=default_params_file,
        description='Docking Server参数文件的完整路径')
        
    declare_autostart_cmd = DeclareLaunchArgument(
        'autostart', 
        default_value='true',
        description='自动启动生命周期节点')
        
    declare_use_composition_cmd = DeclareLaunchArgument(
        'use_composition', 
        default_value='False',
        description='是否使用组合节点')
        
    declare_container_name_cmd = DeclareLaunchArgument(
        'container_name', 
        default_value='nav2_container',
        description='组合节点的容器名称')
        
    declare_use_respawn_cmd = DeclareLaunchArgument(
        'use_respawn', 
        default_value='False',
        description='是否在节点死亡时重新生成')
        
    declare_log_level_cmd = DeclareLaunchArgument(
        'log_level', 
        default_value='info',
        description='日志级别')
    
    # 创建参数字典
    configured_params = RewrittenYaml(
        source_file=params_file,
        root_key=namespace,
        param_rewrites={},
        convert_types=True)
    
    # 激光线段匹配充电桩检测器节点
    laser_line_dock_detector_cmd = Node(
        package='docking_server',
        executable='laser_line_dock_detector',
        name='laser_line_dock_detector',
        output='screen',
        parameters=[configured_params],
        remappings=[
            ('/tf', 'tf'),
            ('/tf_static', 'tf_static'),
            ('/scan', '/scan')
        ],
        arguments=['--ros-args', '--log-level', log_level])
    
    # 生命周期管理器
    lifecycle_manager_cmd = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_docking',
        output='screen',
        parameters=[
            {'use_sim_time': use_sim_time},
            {'autostart': autostart},
            {'node_names': ['laser_line_dock_detector']}
        ])
    
    # 启动信息
    start_info = LogInfo(
        msg=[
            '\n',
            '========================================\n',
            '  RobotCar Docking Server 启动中...\n',
            '========================================\n',
            '功能说明:\n',
            '- 基于激光雷达线段匹配的充电桩检测\n',
            '- 峰谷特征识别算法\n',
            '- 自动对接控制\n',
            '\n',
            '服务接口:\n',
            '- /dock: 开始对接过程\n',
            '- /undock: 开始脱离过程\n',
            '\n',
            '可视化:\n',
            '- /dock_markers: 充电桩和线段可视化\n',
            '========================================\n'
        ]
    )
    
    # 创建launch描述
    ld = LaunchDescription()
    
    # 添加声明参数的动作
    ld.add_action(declare_namespace_cmd)
    ld.add_action(declare_use_sim_time_cmd)
    ld.add_action(declare_params_file_cmd)
    ld.add_action(declare_autostart_cmd)
    ld.add_action(declare_use_composition_cmd)
    ld.add_action(declare_container_name_cmd)
    ld.add_action(declare_use_respawn_cmd)
    ld.add_action(declare_log_level_cmd)
    
    # 添加启动信息
    ld.add_action(start_info)
    
    # 添加节点
    ld.add_action(laser_line_dock_detector_cmd)
    ld.add_action(lifecycle_manager_cmd)
    
    return ld
