# RobotCar 激光雷达线段匹配充电桩检测器

## 📋 概述

这是一个基于激光雷达线段匹配的充电桩检测功能包，用于机器人自动充电对接。该功能包实现了使用激光扫描数据识别充电桩的峰谷特征，并通过线段匹配算法实现精确定位。

## 🎯 主要功能

### 1. 激光雷达线段匹配检测器
- **峰谷特征识别**：识别激光扫描数据中的峰谷特征
- **线段提取与匹配**：从激光数据中提取线段并进行匹配
- **平行线检测**：识别充电桩特征的平行线
- **鲁棒性滤波**：中值滤波和噪声抑制
- **实时可视化**：充电桩和线段的可视化

### 2. 对接控制
- **精确对接**：基于检测到的充电桩位置进行精确对接
- **安全控制**：速度限制和碰撞检测
- **多阶段对接**：预备阶段、接近阶段和最终对接阶段
- **自动脱离**：支持自动脱离充电桩

## 🚀 使用方法

### 1. 编译安装

```bash
# 进入工作空间
cd /home/<USER>/test_ws

# 编译功能包
colcon build --packages-select docking_server --symlink-install

# 加载环境
source install/setup.bash
```

### 2. 启动方式

```bash
# 启动Docking Server
ros2 launch docking_server docking_server.launch.py
```

### 3. 服务接口

Docking Server提供以下服务接口：

- **开始对接**：
```bash
ros2 service call /dock nav2_msgs/srv/Dock "{dock_pose: {header: {frame_id: 'map'}, pose: {position: {x: 1.0, y: 0.0, z: 0.0}, orientation: {w: 1.0}}}}"
```

- **脱离充电桩**：
```bash
ros2 service call /undock std_srvs/srv/Trigger "{}"
```

### 4. 与导航系统集成

#### 完整导航系统启动顺序：

1. **启动机器人基础系统**：
```bash
ros2 launch robotcar_base robotcar.launch.py
```

2. **启动导航系统**：
```bash
ros2 launch robotcar_nav nav_05.launch.py
```

3. **启动Docking Server**：
```bash
ros2 launch docking_server docking_server.launch.py
```

## 🔧 配置说明

### 激光线段检测器配置
```yaml
laser_line_detector:
  plugin: "docking_server::LaserLineDockDetector"
  
  # 传感器配置
  laser_topic: "/scan"                 # 激光雷达话题
  base_frame: "base_footprint"         # 机器人本体坐标系
  
  # 检测参数
  detection_timeout: 2.0               # 检测超时时间 (s)
  max_detection_range: 3.0             # 最大检测距离 (m)
  min_detection_range: 0.15            # 最小检测距离 (m)
  
  # 线段检测参数
  line_segment_min_length: 0.15        # 线段最小长度 (m)
  line_segment_max_gap: 0.03           # 线段最大间隙 (m)
  parallel_tolerance: 0.087            # 平行容差 (rad, ~5度)
  
  # 充电桩几何参数
  dock_width: 0.4                      # 充电桩宽度 (m)
  dock_depth: 0.25                     # 充电桩深度 (m)
```

### 对接控制器配置
```yaml
dock_controller:
  plugin: "nav2_docking::SimpleChargingDock"
  
  # 控制参数
  k_phi: 3.0                          # 角度控制增益
  k_delta: 2.0                        # 位置控制增益
  
  # 速度限制
  v_linear_min: 0.05                  # 最小线速度 (m/s)
  v_linear_max: 0.25                  # 最大线速度 (m/s)
  v_angular_max: 0.75                 # 最大角速度 (rad/s)
```

## 🎮 可视化

Docking Server提供了充电桩和线段的可视化功能，可以在RViz中查看：

1. 在RViz中添加MarkerArray显示
2. 设置话题为 `/dock_markers`
3. 红色立方体表示检测到的充电桩
4. 蓝色线段表示检测到的线段特征

## 🔍 故障排除

### 1. 检测不到充电桩
- 检查激光雷达数据是否正常
- 确认充电桩在激光雷达的检测范围内
- 调整`min_detection_range`和`max_detection_range`参数
- 检查充电桩的几何参数是否与实际一致

### 2. 对接不精确
- 调整控制参数`k_phi`和`k_delta`
- 减小最大速度限制
- 确保充电桩检测稳定

### 3. 可视化不显示
- 确认`publish_visualization`参数设置为true
- 检查RViz中的话题设置
- 确认MarkerArray显示已添加

## 📝 自定义配置

### 修改充电桩几何参数
如果你的充电桩尺寸与默认配置不同，可以修改以下参数：

```yaml
# 充电桩几何参数
dock_width: 0.4                      # 充电桩宽度 (m)
dock_depth: 0.25                     # 充电桩深度 (m)
```

### 调整检测参数
如果检测不稳定，可以尝试调整以下参数：

```yaml
# 线段检测参数
line_segment_min_length: 0.15        # 线段最小长度 (m)
line_segment_max_gap: 0.03           # 线段最大间隙 (m)
parallel_tolerance: 0.087            # 平行容差 (rad)
```

### 优化控制参数
如果对接过程不够平滑，可以调整以下参数：

```yaml
# 控制参数
k_phi: 3.0                          # 角度控制增益
k_delta: 2.0                        # 位置控制增益
beta: 0.4                           # 前瞻距离系数
```

## 🤝 技术支持

如果遇到问题，请检查：
1. ROS2环境是否正确配置
2. 所有依赖包是否已安装
3. 激光雷达是否正常工作
4. 充电桩是否在激光雷达的检测范围内

## 📚 相关文档

- [Nav2 Docking文档](https://navigation.ros.org/tutorials/docs/navigation2_with_docking.html)
- [RobotCar导航系统文档](../robotcar_nav/README.md)
- [激光雷达线段提取算法](https://ieeexplore.ieee.org/document/8202131)
