cmake_minimum_required(VERSION 3.8)
project(docking_server)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_lifecycle REQUIRED)
find_package(nav2_core REQUIRED)
find_package(nav2_msgs REQUIRED)
find_package(nav2_util REQUIRED)
find_package(nav2_behaviors REQUIRED)
find_package(nav2_costmap_2d REQUIRED)

find_package(sensor_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(visualization_msgs REQUIRED)
find_package(std_srvs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(pluginlib REQUIRED)
find_package(angles REQUIRED)
find_package(laser_geometry REQUIRED)

find_package(lifecycle_msgs REQUIRED)

# 包含目录
include_directories(include)

# 创建库
add_library(docking_server_lib SHARED
  plugins/laser_line_dock_detector.cpp
)

# 设置依赖
set(dependencies
  rclcpp
  rclcpp_lifecycle
  nav2_core
  nav2_msgs
  nav2_util
  nav2_behaviors
  nav2_costmap_2d
  sensor_msgs
  geometry_msgs
  visualization_msgs
  std_srvs
  tf2
  tf2_ros
  tf2_geometry_msgs
  pluginlib
  angles
  laser_geometry
  lifecycle_msgs
)

ament_target_dependencies(docking_server_lib ${dependencies})

# 创建可执行文件
add_executable(laser_line_dock_detector
  src/laser_line_dock_detector_main.cpp
)

target_link_libraries(laser_line_dock_detector docking_server_lib)
ament_target_dependencies(laser_line_dock_detector ${dependencies})

# 安装库和可执行文件
install(TARGETS docking_server_lib
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

install(TARGETS laser_line_dock_detector
  DESTINATION lib/${PROJECT_NAME}
)

# 安装头文件
install(DIRECTORY include/
  DESTINATION include/
)

# 安装launch文件
install(DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}
)

# 安装配置文件
install(DIRECTORY config
  DESTINATION share/${PROJECT_NAME}
)

# 安装配置文件
install(FILES
  config/docking_params.yaml
  DESTINATION share/${PROJECT_NAME}/config
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_export_include_directories(include)
ament_export_libraries(docking_server_lib)
ament_export_dependencies(${dependencies})

ament_package()
