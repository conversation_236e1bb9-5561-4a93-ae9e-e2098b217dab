<?xml version="1.0"?>
<library path="docking_server_plugins">
  <class type="docking_server::LaserLineDockDetector" base_class_type="nav2_docking::DockDetector">
    <description>
      Laser-based line segment matching dock detector for charging station docking.
      This detector uses laser scan data to identify charging station features through
      peak-valley pattern recognition and line segment matching algorithms.
      
      Features:
      - Peak-valley feature detection in laser scan data
      - Line segment extraction and matching
      - Parallel line detection for dock identification
      - Robust filtering and noise reduction
      - Real-time visualization support
    </description>
  </class>
</library>
