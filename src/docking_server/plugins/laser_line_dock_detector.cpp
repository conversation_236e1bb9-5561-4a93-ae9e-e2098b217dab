#include "docking_server/plugins/laser_line_dock_detector.hpp"

namespace docking_server
{

LaserLineDockDetector::LaserLineDockDetector()
: rclcpp_lifecycle::LifecycleNode("laser_line_dock_detector"),
  dock_detected_(false)
{
}

rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
LaserLineDockDetector::on_configure(const rclcpp_lifecycle::State & /*state*/)
{
  RCLCPP_INFO(get_logger(), "Configuring LaserLineDockDetector");

  // 创建TF缓冲区和监听器
  tf_buffer_ = std::make_shared<tf2_ros::Buffer>(get_clock());
  tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
  
  // 声明并获取参数
  declare_parameter("laser_topic", "scan");
  declare_parameter("base_frame", "base_footprint");
  declare_parameter("dock_frame", "dock_frame");
  declare_parameter("detection_timeout", 1.0);
  declare_parameter("max_detection_range", 3.0);
  declare_parameter("min_detection_range", 0.1);
  declare_parameter("line_segment_min_length", 0.2);
  declare_parameter("line_segment_max_gap", 0.05);
  declare_parameter("parallel_tolerance", 0.1);
  declare_parameter("dock_width", 0.5);
  declare_parameter("dock_depth", 0.3);
  declare_parameter("median_filter_size", 5);
  declare_parameter("range_filter_threshold", 0.05);
  declare_parameter("publish_visualization", true);

  get_parameter("laser_topic", laser_topic_);
  get_parameter("base_frame", base_frame_);
  get_parameter("dock_frame", dock_frame_);
  get_parameter("detection_timeout", detection_timeout_);
  get_parameter("max_detection_range", max_detection_range_);
  get_parameter("min_detection_range", min_detection_range_);
  get_parameter("line_segment_min_length", line_segment_min_length_);
  get_parameter("line_segment_max_gap", line_segment_max_gap_);
  get_parameter("parallel_tolerance", parallel_tolerance_);
  get_parameter("dock_width", dock_width_);
  get_parameter("dock_depth", dock_depth_);
  get_parameter("median_filter_size", median_filter_size_);
  get_parameter("range_filter_threshold", range_filter_threshold_);
  get_parameter("publish_visualization", publish_visualization_);

  RCLCPP_INFO(
    get_logger(),
    "Configured LaserLineDockDetector with laser_topic: %s, base_frame: %s",
    laser_topic_.c_str(), base_frame_.c_str());

  return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::SUCCESS;
}

rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
LaserLineDockDetector::on_activate(const rclcpp_lifecycle::State & /*state*/)
{
  RCLCPP_INFO(get_logger(), "Activating LaserLineDockDetector");

  // 创建激光扫描订阅者
  laser_sub_ = create_subscription<sensor_msgs::msg::LaserScan>(
    laser_topic_, rclcpp::SensorDataQoS(),
    std::bind(&LaserLineDockDetector::laserCallback, this, std::placeholders::_1));

  // 创建可视化标记发布者
  if (publish_visualization_) {
    marker_pub_ = create_publisher<visualization_msgs::msg::MarkerArray>("dock_markers", 10);
    marker_pub_->on_activate();
  }

  // 创建检测服务
  detect_service_ = create_service<std_srvs::srv::Trigger>(
    "detect_dock",
    std::bind(&LaserLineDockDetector::detectDockService, this,
      std::placeholders::_1, std::placeholders::_2));

  RCLCPP_INFO(get_logger(), "LaserLineDockDetector activated");

  return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::SUCCESS;
}

rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
LaserLineDockDetector::on_deactivate(const rclcpp_lifecycle::State & /*state*/)
{
  RCLCPP_INFO(get_logger(), "Deactivating LaserLineDockDetector");

  laser_sub_.reset();
  detect_service_.reset();

  if (marker_pub_) {
    marker_pub_->on_deactivate();
  }

  RCLCPP_INFO(get_logger(), "LaserLineDockDetector deactivated");

  return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::SUCCESS;
}

rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
LaserLineDockDetector::on_cleanup(const rclcpp_lifecycle::State & /*state*/)
{
  RCLCPP_INFO(get_logger(), "Cleaning up LaserLineDockDetector");

  laser_sub_.reset();
  detect_service_.reset();
  marker_pub_.reset();

  return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::SUCCESS;
}

void LaserLineDockDetector::laserCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg)
{
  latest_scan_ = msg;

  // 过滤激光扫描数据
  auto filtered_scan = filterLaserScan(*msg);

  // 检测线段
  detected_segments_ = detectLineSegments(filtered_scan);

  if (!detected_segments_.empty()) {
    // 识别充电桩
    auto dock_pose = identifyDockingStation(detected_segments_);

    // 创建充电桩位姿消息
    geometry_msgs::msg::PoseStamped pose;
    pose.header.frame_id = latest_scan_->header.frame_id;
    pose.header.stamp = latest_scan_->header.stamp;
    pose.pose = dock_pose;

    // 更新充电桩位置
    dock_pose_ = pose;
    dock_detected_ = true;
    last_detection_time_ = now();

    RCLCPP_DEBUG(
      get_logger(), "Dock detected at position [%f, %f, %f]",
      dock_pose_.pose.position.x, dock_pose_.pose.position.y,
      tf2::getYaw(dock_pose_.pose.orientation));

    // 发布可视化标记
    if (publish_visualization_ && marker_pub_->is_activated()) {
      publishVisualization();
    }
  } else {
    // 检查是否超时
    auto current_time = now();
    if ((current_time - last_detection_time_).seconds() > detection_timeout_) {
      dock_detected_ = false;
    }
  }
}

void LaserLineDockDetector::detectDockService(
  const std::shared_ptr<std_srvs::srv::Trigger::Request> request,
  std::shared_ptr<std_srvs::srv::Trigger::Response> response)
{
  (void)request;  // 未使用的参数

  if (!dock_detected_) {
    response->success = false;
    response->message = "No dock detected";
    return;
  }

  // 检查是否超时
  auto current_time = now();
  if ((current_time - last_detection_time_).seconds() > detection_timeout_) {
    dock_detected_ = false;
    response->success = false;
    response->message = "Dock detection timed out";
    return;
  }

  // 返回成功状态
  response->success = true;
  response->message = "Dock detected successfully at position [" +
                     std::to_string(dock_pose_.pose.position.x) + ", " +
                     std::to_string(dock_pose_.pose.position.y) + "]";
}

std::vector<std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point>>
LaserLineDockDetector::detectLineSegments(const sensor_msgs::msg::LaserScan & scan)
{
  std::vector<std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point>> segments;
  
  // 实现线段检测算法
  // 这里使用简化的分段线性拟合算法
  
  std::vector<geometry_msgs::msg::Point> points;
  
  // 将激光扫描转换为点集
  for (size_t i = 0; i < scan.ranges.size(); i++) {
    float range = scan.ranges[i];
    
    // 忽略无效测量
    if (!std::isfinite(range) || range < min_detection_range_ || range > max_detection_range_) {
      continue;
    }
    
    // 计算点的坐标
    float angle = scan.angle_min + i * scan.angle_increment;
    geometry_msgs::msg::Point point;
    point.x = range * std::cos(angle);
    point.y = range * std::sin(angle);
    point.z = 0.0;
    
    points.push_back(point);
  }
  
  // 如果点太少，无法形成线段
  if (points.size() < 3) {
    return segments;
  }
  
  // 分段线性拟合
  size_t start_idx = 0;
  while (start_idx < points.size() - 2) {
    size_t end_idx = start_idx + 1;
    
    // 初始线段
    auto & p_start = points[start_idx];
    auto & p_end = points[end_idx];
    
    // 尝试扩展线段
    while (end_idx < points.size() - 1) {
      // 检查下一个点是否在线段上
      auto & p_next = points[end_idx + 1];
      
      // 计算点到线段的距离
      double dx = p_end.x - p_start.x;
      double dy = p_end.y - p_start.y;
      double length = std::sqrt(dx * dx + dy * dy);
      
      if (length < 1e-6) {
        // 线段太短，移动到下一个点
        end_idx++;
        p_end = points[end_idx];
        continue;
      }
      
      // 计算点到线的距离
      double t = ((p_next.x - p_start.x) * dx + (p_next.y - p_start.y) * dy) / (length * length);
      double dist;
      
      if (t < 0.0) {
        // 点在线段起点之前
        dist = calculateDistance(p_next, p_start);
      } else if (t > 1.0) {
        // 点在线段终点之后
        dist = calculateDistance(p_next, p_end);
      } else {
        // 点在线段上的投影
        geometry_msgs::msg::Point projection;
        projection.x = p_start.x + t * dx;
        projection.y = p_start.y + t * dy;
        projection.z = 0.0;
        dist = calculateDistance(p_next, projection);
      }
      
      // 如果距离小于阈值，扩展线段
      if (dist < line_segment_max_gap_) {
        end_idx++;
        p_end = points[end_idx];
      } else {
        break;
      }
    }
    
    // 检查线段长度是否满足最小要求
    double segment_length = calculateDistance(p_start, p_end);
    if (segment_length >= line_segment_min_length_) {
      segments.push_back(std::make_pair(p_start, p_end));
    }
    
    // 移动到下一个起点
    start_idx = end_idx + 1;
  }
  
  return segments;
}

geometry_msgs::msg::Pose LaserLineDockDetector::identifyDockingStation(
  const std::vector<std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point>> & segments)
{
  geometry_msgs::msg::Pose dock_pose;
  
  // 如果没有足够的线段，返回空位姿
  if (segments.size() < 2) {
    return dock_pose;
  }
  
  // 查找平行的线段对
  for (size_t i = 0; i < segments.size() - 1; i++) {
    for (size_t j = i + 1; j < segments.size(); j++) {
      const auto & seg1 = segments[i];
      const auto & seg2 = segments[j];
      
      // 检查线段是否平行
      if (areSegmentsParallel(seg1, seg2, parallel_tolerance_)) {
        // 计算两线段之间的距离
        double dx = (seg1.first.x + seg1.second.x) / 2.0 - (seg2.first.x + seg2.second.x) / 2.0;
        double dy = (seg1.first.y + seg1.second.y) / 2.0 - (seg2.first.y + seg2.second.y) / 2.0;
        double distance = std::sqrt(dx * dx + dy * dy);
        
        // 检查距离是否接近充电桩宽度
        if (std::abs(distance - dock_width_) < 0.1) {
          // 计算充电桩中心位置
          dock_pose.position.x = (seg1.first.x + seg1.second.x + seg2.first.x + seg2.second.x) / 4.0;
          dock_pose.position.y = (seg1.first.y + seg1.second.y + seg2.first.y + seg2.second.y) / 4.0;
          dock_pose.position.z = 0.0;
          
          // 计算充电桩方向（垂直于线段）
          double angle1 = calculateAngle(seg1.first, seg1.second);
          double dock_angle = angle1 + M_PI / 2.0;  // 垂直于线段
          
          // 设置方向四元数
          tf2::Quaternion q;
          q.setRPY(0, 0, dock_angle);
          dock_pose.orientation = tf2::toMsg(q);
          
          return dock_pose;
        }
      }
    }
  }
  
  // 未找到匹配的充电桩
  return dock_pose;
}

double LaserLineDockDetector::calculateDistance(
  const geometry_msgs::msg::Point & p1,
  const geometry_msgs::msg::Point & p2)
{
  double dx = p1.x - p2.x;
  double dy = p1.y - p2.y;
  double dz = p1.z - p2.z;
  return std::sqrt(dx * dx + dy * dy + dz * dz);
}

double LaserLineDockDetector::calculateAngle(
  const geometry_msgs::msg::Point & p1,
  const geometry_msgs::msg::Point & p2)
{
  return std::atan2(p2.y - p1.y, p2.x - p1.x);
}

bool LaserLineDockDetector::areSegmentsParallel(
  const std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point> & seg1,
  const std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point> & seg2,
  double tolerance)
{
  double angle1 = calculateAngle(seg1.first, seg1.second);
  double angle2 = calculateAngle(seg2.first, seg2.second);
  
  // 计算角度差异（考虑周期性）
  double angle_diff = std::abs(angles::shortest_angular_distance(angle1, angle2));
  
  // 检查是否平行（角度差接近0或π）
  return angle_diff < tolerance || std::abs(angle_diff - M_PI) < tolerance;
}

sensor_msgs::msg::LaserScan LaserLineDockDetector::filterLaserScan(
  const sensor_msgs::msg::LaserScan & scan)
{
  sensor_msgs::msg::LaserScan filtered_scan = scan;
  
  // 应用中值滤波器去除噪声
  std::vector<float> filtered_ranges(scan.ranges.size());
  
  for (size_t i = 0; i < scan.ranges.size(); i++) {
    // 获取窗口内的点
    std::vector<float> window;
    int half_size = median_filter_size_ / 2;
    
    for (int j = -half_size; j <= half_size; j++) {
      int idx = static_cast<int>(i) + j;
      if (idx >= 0 && idx < static_cast<int>(scan.ranges.size()) && 
          std::isfinite(scan.ranges[idx])) {
        window.push_back(scan.ranges[idx]);
      }
    }
    
    // 如果窗口为空，保持原值
    if (window.empty()) {
      filtered_ranges[i] = scan.ranges[i];
    } else {
      // 计算中值
      std::sort(window.begin(), window.end());
      filtered_ranges[i] = window[window.size() / 2];
    }
  }
  
  filtered_scan.ranges = filtered_ranges;
  return filtered_scan;
}

void LaserLineDockDetector::publishVisualization()
{
  if (!marker_pub_ || !dock_detected_) {
    return;
  }
  
  visualization_msgs::msg::MarkerArray marker_array;
  
  // 创建充电桩标记
  visualization_msgs::msg::Marker dock_marker;
  dock_marker.header = dock_pose_.header;
  dock_marker.ns = "dock";
  dock_marker.id = 0;
  dock_marker.type = visualization_msgs::msg::Marker::CUBE;
  dock_marker.action = visualization_msgs::msg::Marker::ADD;
  dock_marker.pose = dock_pose_.pose;
  
  // 设置充电桩尺寸
  dock_marker.scale.x = dock_depth_;
  dock_marker.scale.y = dock_width_;
  dock_marker.scale.z = 0.1;
  
  // 设置颜色（红色半透明）
  dock_marker.color.r = 1.0;
  dock_marker.color.g = 0.0;
  dock_marker.color.b = 0.0;
  dock_marker.color.a = 0.7;
  
  marker_array.markers.push_back(dock_marker);
  
  // 创建线段标记
  for (size_t i = 0; i < detected_segments_.size(); i++) {
    visualization_msgs::msg::Marker line_marker;
    line_marker.header = dock_pose_.header;
    line_marker.ns = "segments";
    line_marker.id = i;
    line_marker.type = visualization_msgs::msg::Marker::LINE_STRIP;
    line_marker.action = visualization_msgs::msg::Marker::ADD;
    
    // 添加线段端点
    line_marker.points.push_back(detected_segments_[i].first);
    line_marker.points.push_back(detected_segments_[i].second);
    
    // 设置线段属性
    line_marker.scale.x = 0.02;  // 线宽
    
    // 设置颜色（蓝色）
    line_marker.color.r = 0.0;
    line_marker.color.g = 0.0;
    line_marker.color.b = 1.0;
    line_marker.color.a = 1.0;
    
    marker_array.markers.push_back(line_marker);
  }
  
  // 发布标记
  marker_pub_->publish(marker_array);
}

}  // namespace docking_server
