<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>docking_server</name>
  <version>0.0.0</version>
  <description>Nav2 Docking Server implementation with laser-based line segment matching for charging station docking</description>
  <maintainer email="<EMAIL>">xrobot</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>nav2_core</depend>
  <depend>nav2_msgs</depend>
  <depend>nav2_util</depend>
  <depend>nav2_behaviors</depend>
  <depend>nav2_costmap_2d</depend>
  <depend>sensor_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>std_srvs</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>pluginlib</depend>
  <depend>angles</depend>
  <depend>laser_geometry</depend>
  <depend>lifecycle_msgs</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>visualization_msgs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
