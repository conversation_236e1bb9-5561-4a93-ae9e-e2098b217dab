#ifndef DOCKING_SERVER__PLUGINS__LASER_LINE_DOCK_DETECTOR_HPP_
#define DOCKING_SERVER__PLUGINS__LASER_LINE_DOCK_DETECTOR_HPP_

#include <memory>
#include <string>
#include <vector>
#include <algorithm>
#include <cmath>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp_lifecycle/lifecycle_node.hpp"
#include "sensor_msgs/msg/laser_scan.hpp"
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "geometry_msgs/msg/pose.hpp"
#include "std_srvs/srv/trigger.hpp"
#include "tf2/utils.h"
#include "tf2_ros/buffer.h"
#include "tf2_ros/transform_listener.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "nav2_util/node_utils.hpp"
#include "nav2_util/geometry_utils.hpp"
#include "nav2_util/lifecycle_node.hpp"
#include "angles/angles.h"
#include "visualization_msgs/msg/marker_array.hpp"
#include "bondcpp/bond.hpp"

namespace docking_server
{

/**
 * @brief 激光雷达线段匹配充电桩检测器
 *
 * 该类实现了基于激光雷达数据的充电桩检测功能，通过识别激光扫描中的
 * 峰谷特征来定位充电桩位置。主要用于机器人自动充电对接。
 */
class LaserLineDockDetector : public rclcpp_lifecycle::LifecycleNode
{
public:
  /**
   * @brief 构造函数
   */
  LaserLineDockDetector();

  /**
   * @brief 析构函数
   */
  ~LaserLineDockDetector() = default;

  /**
   * @brief 配置生命周期节点
   */
  rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
  on_configure(const rclcpp_lifecycle::State & state) override;

  /**
   * @brief 激活生命周期节点
   */
  rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
  on_activate(const rclcpp_lifecycle::State & state) override;

  /**
   * @brief 停用生命周期节点
   */
  rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
  on_deactivate(const rclcpp_lifecycle::State & state) override;

  /**
   * @brief 清理生命周期节点
   */
  rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
  on_cleanup(const rclcpp_lifecycle::State & state) override;

  /**
   * @brief 检测充电桩位置服务回调
   * @param request 服务请求
   * @param response 服务响应
   */
  void detectDockService(
    const std::shared_ptr<std_srvs::srv::Trigger::Request> request,
    std::shared_ptr<std_srvs::srv::Trigger::Response> response);

protected:
  /**
   * @brief 激光扫描回调函数
   * @param msg 激光扫描消息
   */
  void laserCallback(const sensor_msgs::msg::LaserScan::SharedPtr msg);

  /**
   * @brief 检测线段特征
   * @param scan 激光扫描数据
   * @return 检测到的线段列表
   */
  std::vector<std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point>>
  detectLineSegments(const sensor_msgs::msg::LaserScan & scan);

  /**
   * @brief 识别充电桩特征
   * @param segments 线段列表
   * @return 充电桩位置和方向
   */
  geometry_msgs::msg::Pose identifyDockingStation(
    const std::vector<std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point>> & segments);

  /**
   * @brief 计算两点之间的距离
   * @param p1 点1
   * @param p2 点2
   * @return 距离
   */
  double calculateDistance(
    const geometry_msgs::msg::Point & p1,
    const geometry_msgs::msg::Point & p2);

  /**
   * @brief 计算线段角度
   * @param p1 起点
   * @param p2 终点
   * @return 角度（弧度）
   */
  double calculateAngle(
    const geometry_msgs::msg::Point & p1,
    const geometry_msgs::msg::Point & p2);

  /**
   * @brief 检查线段是否平行
   * @param seg1 线段1
   * @param seg2 线段2
   * @param tolerance 角度容差
   * @return 是否平行
   */
  bool areSegmentsParallel(
    const std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point> & seg1,
    const std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point> & seg2,
    double tolerance);

  /**
   * @brief 滤波激光扫描数据
   * @param scan 原始扫描数据
   * @return 滤波后的扫描数据
   */
  sensor_msgs::msg::LaserScan filterLaserScan(const sensor_msgs::msg::LaserScan & scan);

  /**
   * @brief 发布可视化标记
   */
  void publishVisualization();

  // TF相关
  std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
  std::shared_ptr<tf2_ros::TransformListener> tf_listener_;

  // 订阅者、发布者和服务
  rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr laser_sub_;
  rclcpp_lifecycle::LifecyclePublisher<visualization_msgs::msg::MarkerArray>::SharedPtr marker_pub_;
  rclcpp::Service<std_srvs::srv::Trigger>::SharedPtr detect_service_;
  
  // 参数
  std::string laser_topic_;
  std::string base_frame_;
  std::string dock_frame_;
  double detection_timeout_;
  double max_detection_range_;
  double min_detection_range_;
  double line_segment_min_length_;
  double line_segment_max_gap_;
  double parallel_tolerance_;
  double dock_width_;
  double dock_depth_;
  int median_filter_size_;
  double range_filter_threshold_;
  bool publish_visualization_;
  
  // 状态变量
  sensor_msgs::msg::LaserScan::SharedPtr latest_scan_;
  geometry_msgs::msg::PoseStamped dock_pose_;
  bool dock_detected_;
  rclcpp::Time last_detection_time_;
  std::vector<std::pair<geometry_msgs::msg::Point, geometry_msgs::msg::Point>> detected_segments_;
  
  // 日志
  rclcpp::Logger logger_{rclcpp::get_logger("LaserLineDockDetector")};
};

}  // namespace docking_server

#endif  // DOCKING_SERVER__PLUGINS__LASER_LINE_DOCK_DETECTOR_HPP_
