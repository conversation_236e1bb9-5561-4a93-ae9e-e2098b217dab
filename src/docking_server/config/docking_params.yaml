# ========================================
# Nav2 Docking Server 配置文件
# ========================================
# 基于激光雷达线段匹配的充电桩对接配置
# 适配robotcar导航系统
# 作者: Augment Agent
# 日期: 2025-07-15

# ========== 激光线段检测器配置 ==========
    
laser_line_dock_detector:
  ros__parameters:
    # 传感器配置 - 适配robotcar激光雷达
    laser_topic: "/scan"                 # 激光雷达话题
    base_frame: "base_footprint"         # 机器人本体坐标系
    dock_frame: "dock_frame"             # 充电桩坐标系

    # 检测参数
    detection_timeout: 2.0               # 检测超时时间 (s)
    max_detection_range: 3.0             # 最大检测距离 (m)
    min_detection_range: 0.15            # 最小检测距离 (m) - 适配robotcar激光雷达

    # 线段检测参数 - 针对充电桩峰谷特征优化
    line_segment_min_length: 0.15        # 线段最小长度 (m)
    line_segment_max_gap: 0.03           # 线段最大间隙 (m)
    parallel_tolerance: 0.087            # 平行容差 (rad, ~5度)

    # 充电桩几何参数 - 根据实际充电桩尺寸调整
    dock_width: 0.4                      # 充电桩宽度 (m)
    dock_depth: 0.25                     # 充电桩深度 (m)

    # 滤波参数 - 针对robotcar激光雷达噪声特性
    median_filter_size: 5                # 中值滤波器大小
    range_filter_threshold: 0.05         # 距离滤波阈值 (m)

    # 可视化配置
    publish_visualization: true          # 发布可视化标记
    
# ========== 生命周期管理器配置 ==========

# ========== 生命周期管理器配置 ==========
lifecycle_manager_docking:
  ros__parameters:
    use_sim_time: false                   # 使用真实时间
    autostart: true                       # 自动启动
    
    # 管理的节点列表
    node_names: ['docking_server']
    
    # 启动顺序和依赖
    bond_timeout: 4.0                     # 绑定超时时间 (s)
    attempt_respawn_reconnection: true    # 尝试重新连接

# ========== 峰谷特征检测高级参数 ==========
# 这些参数用于微调峰谷特征识别算法
peak_valley_detection:
  ros__parameters:
    # 峰谷检测参数
    peak_threshold: 0.1                   # 峰值检测阈值 (m)
    valley_threshold: 0.1                 # 谷值检测阈值 (m)
    min_peak_prominence: 0.05             # 最小峰值突出度 (m)
    min_valley_depth: 0.05                # 最小谷值深度 (m)
    
    # 特征匹配参数
    feature_match_tolerance: 0.1          # 特征匹配容差 (m)
    min_feature_distance: 0.2             # 最小特征间距 (m)
    max_feature_distance: 0.8             # 最大特征间距 (m)
    
    # 充电桩模式识别
    expected_dock_pattern: "valley_peak_valley"  # 期望的充电桩模式
    pattern_confidence_threshold: 0.7     # 模式置信度阈值
    
    # 时间滤波
    temporal_filter_window: 5             # 时间滤波窗口大小
    detection_stability_threshold: 3      # 检测稳定性阈值
