<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>apriltag_ros</name>
  <version>3.2.2</version>
  <description>AprilTag detection node</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>MIT</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>eigen</build_depend>

  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>sensor_msgs</depend>
  <depend>tf2_ros</depend>
  <depend>apriltag_msgs</depend>
  <depend>apriltag</depend>
  <depend>image_transport</depend>
  <depend>cv_bridge</depend>

  <exec_depend>camera_ros</exec_depend>
  <exec_depend condition="$ROS_DISTRO != 'humble'">image_proc</exec_depend>
  <exec_depend>image_transport_plugins</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_cmake_clang_format</test_depend>
  <test_depend>clang-format</test_depend>
  <test_depend>ament_cmake_cppcheck</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
