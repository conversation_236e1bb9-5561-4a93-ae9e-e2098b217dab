launch:
- arg:
    name: device
    default: "0"

- node_container:
    pkg: rclcpp_components
    exec: component_container
    name: apriltag_container
    namespace: ""
    composable_node:
    - pkg: camera_ros
      plugin: camera::CameraNode
      name: camera
      namespace: camera
      param:
      - name: camera
        value: $(var device)
      - name: camera_info_url
        value: "file://$(find-pkg-share apriltag_ros)/cfg/ost.yaml" 
      - name: width
        value: 1920
      - name: height
        value: 1080
      - name: format
        value: "MJPEG"
      extra_arg:
      - name: use_intra_process_comms
        value: "True"

    - pkg: image_proc
      plugin: image_proc::RectifyNode
      name: rectify
      namespace: camera
      remap:
      - from: image
        to: /camera/camera/image_raw
      - from: camera_info
        to: /camera/camera/camera_info
      extra_arg:
      - name: use_intra_process_comms
        value: "True"

    - pkg: apriltag_ros
      plugin: AprilTagNode
      name: apriltag
      namespace: apriltag
      remap:
      - from: /apriltag/image_rect
        to: /camera/image_rect
      - from: /camera/camera_info
        to: /camera/camera/camera_info
      param:
      - from: $(find-pkg-share apriltag_ros)/cfg/tags_36h11.yaml
      extra_arg:
      - name: use_intra_process_comms
        value: "True"

- node:
    pkg: tf2_ros
    exec: static_transform_publisher
    name: static_tf_pub_camera
    args: "--x 0.32 --y 0.0 --z 0.2 --qx -0.5 --qy 0.5 --qz -0.5 --qw 0.5 --frame-id base_link --child-frame-id camera"



