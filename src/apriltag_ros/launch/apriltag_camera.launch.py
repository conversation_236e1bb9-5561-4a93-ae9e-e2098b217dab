from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # 定义启动参数
    device_arg = DeclareLaunchArgument(
        'device',
        default_value='0',
        description='Camera device ID'
    )

    # 获取apriltag_ros包的路径
    apriltag_ros_share_dir = get_package_share_directory('apriltag_ros')

    # 摄像头节点
    camera_node = Node(
        package='camera_ros',
        executable='camera_node',
        name='camera',
        namespace='camera',
        parameters=[{
            'camera': LaunchConfiguration('device'),
            'camera_info_url': f"file://{apriltag_ros_share_dir}/cfg/ost.yaml",
            'width': 1920,
            'height': 1080,
            'format': 'MJPEG'
        }],
        output='screen'
    )

    # 图像校正节点
    rectify_node = Node(
        package='image_proc',
        executable='rectify_node',
        name='rectify',
        namespace='camera',
        remappings=[
            ('image', '/camera/camera/image_raw'),
            ('camera_info', '/camera/camera/camera_info')
        ],
        output='screen'
    )

    # AprilTag检测节点
    apriltag_node = Node(
        package='apriltag_ros',
        executable='apriltag_node',
        name='apriltag',
        namespace='apriltag',
        remappings=[
            ('/apriltag/image_rect', '/camera/image_rect'),
            ('/camera/camera_info', '/camera/camera/camera_info')
        ],
        parameters=[f"{apriltag_ros_share_dir}/cfg/tags_36h11.yaml"],
        output='screen'
    )

    # 静态TF发布节点
    static_tf_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='static_tf_pub_camera',
        arguments=[
            '--x', '0.3585',
            '--y', '0.0',
            '--z', '0.24425',
            '--qx', '-0.5',
            '--qy', '0.5',
            '--qz', '-0.5',
            '--qw', '0.5',
            '--frame-id', 'base_link',
            '--child-frame-id', 'camera'
        ],
        output='screen'
    )

    return LaunchDescription([
        device_arg,
        camera_node,
        rectify_node,
        apriltag_node,
        static_tf_node
    ])
