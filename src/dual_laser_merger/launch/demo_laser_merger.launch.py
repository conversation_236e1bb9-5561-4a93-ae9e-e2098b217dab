# Copyright 2024 pradyum
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import ExecuteProcess
from launch_ros.actions import ComposableNodeContainer, Node
from launch_ros.descriptions import ComposableNode


def generate_launch_description():

    ld = LaunchDescription()

    # 使用0729的bag数据进行调试 - 直接使用绝对路径
    bag_file_path = "/home/<USER>/test_ws/src/dual_laser_merger/bag/dual_lidar/0729"

    play_bag_node = ExecuteProcess(
        cmd=['ros2', 'bag', 'play', bag_file_path, '--loop', '--clock'],
        output='screen',
        shell=False,
    )

    ld.add_action(play_bag_node)

    dual_laser_merger_node = ComposableNodeContainer(
        name='demo_container',
        namespace='',
        package='rclcpp_components',
        executable='component_container',
        composable_node_descriptions=[
            ComposableNode(
                package='dual_laser_merger',
                plugin='merger_node::MergerNode',
                name='dual_laser_merger',
                parameters=[
                    # 时间同步配置
                    {'use_sim_time': True},  # 使用仿真时间解决bag播放时间同步问题
                    # 话题配置 - 使用相对路径，由PushRosNamespace自动添加命名空间
                    {'laser_1_topic': 'lidar1/scan'},
                    {'laser_2_topic': 'lidar2/scan'},
                    {'merged_scan_topic': 'merged_debug'},
                    {'merged_cloud_topic': 'merged_cloud_debug'},
                    {'target_frame': 'base_link'},  # 使用标准坐标系名称
                    # 激光雷达校准参数 - 使用您测量的精确值，方便调试
                    {'laser_1_x_offset': 0.32},      # 前雷达X偏移
                    {'laser_1_y_offset': -0.225},    # 前雷达Y偏移
                    {'laser_1_yaw_offset': -0.7783981},  # 前雷达Yaw偏移 (-45度)
                    {'laser_2_x_offset': -0.318},    # 后雷达X偏移
                    {'laser_2_y_offset': 0.276},     # 后雷达Y偏移
                    {'laser_2_yaw_offset': 2.3611944},   # 后雷达Yaw偏移 (135度)
                    # 扫描参数 - 优化用于oradar雷达
                    {'tolerance': 0.02},              # 时间同步容差
                    {'queue_size': 5},                # 队列大小
                    {'angle_increment': 0.0087266},   # 匹配oradar分辨率
                    {'scan_time': 0.04},              # 25Hz匹配
                    {'range_min': 0.05},              # 匹配oradar设置
                    {'range_max': 20.0},              # 匹配oradar设置
                    {'min_height': -1.0},
                    {'max_height': 1.0},
                    {'angle_min': -3.141592654},
                    {'angle_max': 3.141592654},
                    {'inf_epsilon': 1.0},
                    {'use_inf': True},
                    # 滤波参数 - 启用调试功能
                    {'allowed_radius': 0.6},          # 阴影滤波半径
                    {'enable_shadow_filter': True},   # 启用阴影滤波
                    {'enable_average_filter': False}, # 关闭平均滤波便于调试
                    {'enable_calibration': True},     # 启用校准功能用于实时调参
                    ],
            )
        ],
        output='screen',
    )

    ld.add_action(dual_laser_merger_node)

    # RViz2可视化 - 暂时禁用以避免显示问题
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        output='both',
        arguments=[
            '-d',
            f"{get_package_share_directory('dual_laser_merger')}/config/rviz_config.rviz",
        ],
    )
    ld.add_action(rviz_node)

    return ld
