#!/usr/bin/env python3
# 
# 适配oradar双雷达的demo_laser_merger启动文件
# 去掉bag文件播放，直接使用实际雷达数据
#
from launch import LaunchDescription
from launch_ros.actions import Composable<PERSON>odeContainer, Node
from launch_ros.descriptions import ComposableNode
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # 启动参数
    use_sim_time = LaunchConfiguration('use_sim_time')
    namespace = LaunchConfiguration('namespace')

    # dual_laser_merger节点 - 适配您的雷达配置
    dual_laser_merger_node = ComposableNodeContainer(
        name='oradar_merger_container',
        namespace='',
        package='rclcpp_components',
        executable='component_container',
        composable_node_descriptions=[
            ComposableNode(
                package='dual_laser_merger',
                plugin='merger_node::MergerNode',
                name='dual_laser_merger',
                parameters=[
                    {'use_sim_time': use_sim_time},
                    # 话题配置 - 使用相对路径，由PushRosNamespace自动添加命名空间
                    {'laser_1_topic': 'lidar1/scan'},
                    {'laser_2_topic': 'lidar2/scan'},
                    {'merged_scan_topic': 'merged'},  # 发布到命名空间内
                    {'merged_cloud_topic': 'merged_cloud'},  # 发布到命名空间内
                    {'target_frame': 'base_link'},  # 使用标准坐标系名称
                    
                    # 激光雷达校准参数 - 使用dual_ms500_with_merger.launch的精确位置
                    {'laser_1_x_offset': 0.326},     # 前雷达X偏移 (更精确的值)
                    {'laser_1_y_offset': -0.263},    # 前雷达Y偏移 (更精确的值)
                    {'laser_1_yaw_offset': -0.7783981},  # 前雷达Yaw偏移 (-45度)
                    {'laser_2_x_offset': -0.326},    # 后雷达X偏移 (更精确的值)
                    {'laser_2_y_offset': 0.263},     # 后雷达Y偏移 (更精确的值)
                    {'laser_2_yaw_offset': 2.3611944},   # 后雷达Yaw偏移 (135度)
                    
                    # 扫描参数 - 优化后的配置
                    {'tolerance': 0.02},              # 更严格的时间同步
                    {'queue_size': 5},                # 减少延迟
                    {'angle_increment': 0.0087266},   # 精确匹配oradar分辨率
                    {'scan_time': 0.04},              # 25Hz匹配
                    {'range_min': 0.05},              # 匹配oradar设置
                    {'range_max': 20.0},              # 匹配oradar设置
                    {'min_height': -1.0},             # 扩大高度范围
                    {'max_height': 1.0},              # 扩大高度范围
                    {'angle_min': -3.141592654},      # 全360度
                    {'angle_max': 3.141592654},
                    {'inf_epsilon': 1.0},
                    {'use_inf': True},
                    
                    # 滤波参数 - 启用有用功能
                    {'allowed_radius': 0.6},          # 阴影滤波半径
                    {'enable_shadow_filter': True},   # 启用阴影滤波
                    {'enable_average_filter': False},  # 启用平均滤波
                    {'enable_calibration': False},     # 启用校准功能
                ],
            )
        ],
        output='screen',
    )

    return LaunchDescription([
        # === 启动参数声明 ===
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation time if true'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='',
            description='ROS2 namespace for multi-robot isolation'
        ),

        # === 激光雷达融合节点 ===
        dual_laser_merger_node,

        # RViz2可视化 (可选)
        Node(
            package='rviz2',
            executable='rviz2',
            output='both',
            arguments=[
                '-d',
                f"{get_package_share_directory('dual_laser_merger')}/config/rviz_config.rviz",
            ],
        ),
    ])
