from launch import LaunchDescription
from launch_ros.actions import ComposableNodeContainer, Node, PushRosNamespace, SetRemap
from launch_ros.descriptions import ComposableNode
from launch.substitutions import LaunchConfiguration
from launch.actions import DeclareLaunchArgument, GroupAction

def generate_launch_description():
    # 启动参数
    use_sim_time = LaunchConfiguration('use_sim_time')
    namespace = LaunchConfiguration('namespace')

    # {{ AURA-X: Fix - 重新创建完整的dual_laser_merger配置，包含必需的target_frame参数. }}
    # 构建节点容器
    container_node = ComposableNodeContainer(
        name='oradar_merger_container',
        namespace='',  # 在GroupAction内部，namespace由PushRosNamespace处理
        package='rclcpp_components',
        executable='component_container',
        composable_node_descriptions=[
            ComposableNode(
                package='dual_laser_merger',
                plugin='merger_node::MergerNode',
                name='dual_laser_merger',
                parameters=[
                    {'use_sim_time': use_sim_time},
                    # 关键参数 - target_frame必须设置，否则节点会报错
                    {'target_frame': 'base_footprint'},  # {{ AURA-X: Fix - 设置target_frame为base_footprint. }}
                    # 话题配置
                    {'laser_1_topic': 'lidar1/scan'},
                    {'laser_2_topic': 'lidar2/scan'},
                    {'merged_scan_topic': 'merged'},
                    {'merged_cloud_topic': 'merged_cloud'},

                    # 激光雷达校准参数
                    {'laser_1_x_offset': 0.0},     # 前雷达X偏移
                    {'laser_1_y_offset': 0.0},    # 前雷达Y偏移
                    {'laser_1_yaw_offset': 0.0},  # 前雷达Yaw偏移
                    {'laser_2_x_offset': 0.0},    # 后雷达X偏移
                    {'laser_2_y_offset': 0.0},     # 后雷达Y偏移
                    {'laser_2_yaw_offset': 0.0},   # 后雷达Yaw偏移

                    # 扫描参数
                    {'tolerance': 0.02},
                    {'queue_size': 5},
                    {'angle_increment': 0.0087266},
                    {'scan_time': 0.04},
                    {'range_min': 0.05},
                    {'range_max': 20.0},
                    {'min_height': -1.0},
                    {'max_height': 1.0},
                    {'angle_min': -3.141592654},
                    {'angle_max': 3.141592654},
                    {'inf_epsilon': 1.0},
                    {'use_inf': True},

                    # 滤波参数
                    {'allowed_radius': 0.6},
                    {'enable_shadow_filter': True},
                    {'enable_average_filter': False},
                    {'enable_calibration': True},
                ],
            )
        ],
        output='screen',
    )

    # 根据命名空间是否为空来决定是否使用PushRosNamespace和TF重映射
    if namespace:
        # 有命名空间时使用完整的命名空间配置
        dual_laser_merger_group = GroupAction([
            PushRosNamespace(namespace=namespace),
            SetRemap("tf", "/tf"),  # TF重映射，将命名空间内的tf重映射到全局
            SetRemap("tf_static", "/tf_static"),  # TF重映射，将命名空间内的tf_static重映射到全局
            container_node
        ])
    else:
        # 无命名空间时直接使用容器节点
        dual_laser_merger_group = GroupAction([container_node])

    return LaunchDescription([
        # === 启动参数声明 ===
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation time if true'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='',  # {{ AURA-X: Modify - 默认为空命名空间. }}
            description='ROS2 namespace (optional, default: empty for root namespace)'
        ),

        # === 激光雷达融合节点 ===
        dual_laser_merger_group,
    ])
