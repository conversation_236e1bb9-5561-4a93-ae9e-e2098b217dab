<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>dual_laser_merger</name>
  <version>0.1.1</version>
  <description>merge dual lidar's scans.</description>
  <maintainer email="<EMAIL>">pradyum</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_auto</buildtool_depend>

  <depend>message_filters</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_components</depend>
  <depend>pcl_ros</depend>
  <depend>pcl_conversions</depend>
  <depend>libpcl-all-dev</depend>
  <depend>laser_geometry</depend>
  <depend>tf2</depend>
  <depend>tf2_ros</depend>
  <depend>tf2_sensor_msgs</depend>
  <depend>geometry_msgs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_cpplint</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>ament_xmllint</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
