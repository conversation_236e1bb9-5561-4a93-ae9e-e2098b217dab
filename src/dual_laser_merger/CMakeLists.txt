cmake_minimum_required(VERSION 3.5)
project(dual_laser_merger)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()

ament_auto_add_library(dual_laser_merger SHARED
  src/dual_laser_merger.cpp)

rclcpp_components_register_node(dual_laser_merger
  PLUGIN "merger_node::MergerNode"
  EXECUTABLE dual_laser_merger_node)

# 安装头文件由 ament_auto_package(USE_SCOPED_HEADER_INSTALL_DIR) 处理
# 避免重复安装与路径不一致
# install(DIRECTORY include DESTINATION include/dual_laser_merger)
install(DIRECTORY launch/ DESTINATION share/${PROJECT_NAME}/launch)
install(DIRECTORY bag DESTINATION share/${PROJECT_NAME})
install(DIRECTORY config DESTINATION share/${PROJECT_NAME})

if(BUILD_TESTING)
  #find_package(ament_lint_auto REQUIRED)
  #set(ament_cmake_copyright_FOUND TRUE)
  #set(ament_cmake_cpplint_FOUND TRUE)
  #set(ament_flake8_FOUND TRUE)
  #set(ament_pep257_FOUND TRUE)
  #set(ament_xmllint_FOUND TRUE)
# 使用 scoped 头文件安装目录，避免Kilted Kaiju行为变化的警告
ament_auto_package(USE_SCOPED_HEADER_INSTALL_DIR)

  #ament_lint_auto_find_test_dependencies()
endif()
