---
name: Humble build_test

on:
  workflow_dispatch:

  push:
    branches:
      - humble
  
  pull_request:
    branches:
      - humble

jobs:
  test_build_humble:
    runs-on: ubuntu-latest
    container:
      image: ros:humble-ros-core
    steps:
      - uses: ros-tooling/setup-ros@v0.7
      - uses: ros-tooling/action-ros-ci@v0.3
        with:
          target-ros2-distro: humble
          skip-tests: false
          rosdep-check: true
          package-name: 'dual_laser_merger'
