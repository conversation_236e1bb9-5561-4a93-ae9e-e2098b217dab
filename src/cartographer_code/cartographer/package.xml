<?xml version="1.0"?>
<!--
  Copyright 2016 The Cartographer Authors

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<package format="3">
  <name>cartographer</name>
  <!--
  We add 900 to the patch part of the version and then multiply it by 10,
  i.e. our version = `(upstream_patch_version + 900) * 10`,
  so we can have intermediate releases as well as release any future official 2.0.x versions.

  This is basically packing the patch part of the version and a fourth version part together
  into the third part of the version.

  The use of `900` instead of something else like `100` is arbitrary, but it might
  help people recognize that this is a "special" version number.
  It is needed however, because we cannot have a leading `0` in our patch version.

  Consider these possible future versions as an example:

  2.0.9000 -> current state of this repository, 2.0.0 + some commits from us
  2.0.9010 -> upstream 2.0.1
  2.0.9011 -> upstream 2.0.1 + additional commits from upstream or us
  2.0.9012 -> upstream 2.0.1 + additional commits from 2.0.1011 + more new commits
  2.0.9020 -> upstream 2.0.2
  and so on...
  -->
  <version>2.0.9002</version>
  <description>
    Cartographer is a system that provides real-time simultaneous localization
    and mapping (SLAM) in 2D and 3D across multiple platforms and sensor
    configurations.
  </description>
  <maintainer email="<EMAIL>">Chris Lalancette</maintainer>
  <maintainer email="<EMAIL>">Michael Carroll</maintainer>
  <license>Apache 2.0</license>

  <url>https://github.com/cartographer-project/cartographer</url>

  <author email="<EMAIL>">
    The Cartographer Authors
  </author>
  <author email="<EMAIL>">Darby Lim</author>
  <author email="<EMAIL>">Pyo</author>

  <buildtool_depend>cmake</buildtool_depend>

  <build_depend>git</build_depend>
  <build_depend>google-mock</build_depend>
  <build_depend>gtest</build_depend>
  <build_depend>python3-sphinx</build_depend>

  <depend>libboost-iostreams-dev</depend>
  <depend>eigen</depend>
  <depend>libabsl-dev</depend>
  <depend>libcairo2-dev</depend>
  <depend>libceres-dev</depend>
  <depend>libgflags-dev</depend>
  <depend>libgoogle-glog-dev</depend>
  <depend>lua5.2-dev</depend>
  <depend>protobuf-dev</depend>

  <export>
    <build_type>cmake</build_type>
  </export>
</package>
