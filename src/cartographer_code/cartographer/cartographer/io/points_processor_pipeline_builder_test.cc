/*
 * Copyright 2017 The Cartographer Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "cartographer/io/points_processor_pipeline_builder.h"

#include <string>

#include "cartographer/common/port.h"
#include "cartographer/io/file_writer.h"
#include "gtest/gtest.h"

namespace cartographer {
namespace io {
namespace {

TEST(PointsProcessorPipelineBuilderTest, RegisterBuiltInPointsProcessors) {
  PointsProcessorPipelineBuilder builder;
  FileWriterFactory dummy_factory =
      [](const std::string& filename) -> std::unique_ptr<FileWriter> {
    return nullptr;
  };
  RegisterBuiltInPointsProcessors({}, dummy_factory, &builder);
}

}  // namespace
}  // namespace io
}  // namespace cartographer
