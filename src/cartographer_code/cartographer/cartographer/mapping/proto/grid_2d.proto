// Copyright 2018 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

import "cartographer/mapping/proto/map_limits.proto";
import "cartographer/mapping/proto/probability_grid.proto";
import "cartographer/mapping/proto/tsdf_2d.proto";

message Grid2D {
  message CellBox {
    int32 max_x = 1;
    int32 max_y = 2;
    int32 min_x = 3;
    int32 min_y = 4;
  }

  MapLimits limits = 1;
  // These values are actually int16s, but protos don't have a native int16
  // type.
  repeated int32 cells = 2;
  CellBox known_cells_box = 3;
  oneof grid {
    ProbabilityGrid probability_grid_2d = 4;
    TSDF2D tsdf_2d = 5;
  }
  float min_correspondence_cost = 6;
  float max_correspondence_cost = 7;
}
