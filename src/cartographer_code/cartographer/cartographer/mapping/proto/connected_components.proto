// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package cartographer.mapping.proto;

// This is how proto2 calls the outer class since there is already a message
// with the same name in the file.
option java_outer_classname = "ConnectedComponentsOuterClass";

// Connectivity structure between trajectories.
message ConnectedComponents {
  message ConnectedComponent {
    repeated int32 trajectory_id = 1;
  }

  repeated ConnectedComponent connected_component = 1;
}
