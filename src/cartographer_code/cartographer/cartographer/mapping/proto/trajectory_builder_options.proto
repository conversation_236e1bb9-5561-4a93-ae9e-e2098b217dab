// Copyright 2016 The Cartographer Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "cartographer/transform/proto/transform.proto";
import "cartographer/mapping/proto/motion_filter_options.proto";
import "cartographer/mapping/proto/local_trajectory_builder_options_2d.proto";
import "cartographer/mapping/proto/local_trajectory_builder_options_3d.proto";

package cartographer.mapping.proto;

message InitialTrajectoryPose {
  transform.proto.Rigid3d relative_pose = 1;
  int32 to_trajectory_id = 2;
  int64 timestamp = 3;
}

message TrajectoryBuilderOptions {
  LocalTrajectoryBuilderOptions2D trajectory_builder_2d_options = 1;
  LocalTrajectoryBuilderOptions3D trajectory_builder_3d_options = 2;
  InitialTrajectoryPose initial_trajectory_pose = 4;

  reserved 5;

  bool pure_localization = 3 [deprecated = true];
  message PureLocalizationTrimmerOptions {
    int32 max_submaps_to_keep = 1;
  }
  PureLocalizationTrimmerOptions pure_localization_trimmer = 6;

  bool collate_fixed_frame = 7;
  bool collate_landmarks = 8;

  MotionFilterOptions pose_graph_odometry_motion_filter = 9;
}

message SensorId {
  enum SensorType {
    RANGE = 0;
    IMU = 1;
    ODOMETRY = 2;
    FIXED_FRAME_POSE = 3;
    LANDMARK = 4;
    LOCAL_SLAM_RESULT = 5;
  }

  SensorType type = 1;
  string id = 2;
}

message TrajectoryBuilderOptionsWithSensorIds {
  repeated SensorId sensor_id = 1;
  TrajectoryBuilderOptions trajectory_builder_options = 2;
}

message AllTrajectoryBuilderOptions {
  repeated TrajectoryBuilderOptionsWithSensorIds options_with_sensor_ids = 1;
}
