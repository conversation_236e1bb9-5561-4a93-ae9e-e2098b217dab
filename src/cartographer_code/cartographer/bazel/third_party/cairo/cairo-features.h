/*
 * Copyright 2018 The Cartographer Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef CAIRO_FEATURES_H
#define CAIRO_FEATURES_H

#define CAIRO_HAS_FC_FONT 1
#define CAIRO_HAS_FT_FONT 1
#define CAIRO_HAS_GOBJECT_FUNCTIONS 1
#define CAIRO_HAS_IMAGE_SURFACE 1
#define CAIRO_HAS_MIME_SURFACE 1
#define CAIRO_HAS_OBSERVER_SURFACE 1
#define CAIRO_HAS_PDF_SURFACE 1
#define CAIRO_HAS_PNG_FUNCTIONS 1
#define CAIRO_HAS_PS_SURFACE 1
#define CAIRO_HAS_RECORDING_SURFACE 1
#define CAIRO_HAS_SCRIPT_SURFACE 1
#define CAIRO_HAS_SVG_SURFACE 1
#define CAIRO_HAS_TEE_SURFACE 1
#define CAIRO_HAS_USER_FONT 1

#define CAIRO_HAS_GIF_FUNCTIONS 1
#define CAIRO_HAS_JPEG_FUNCTIONS 1

/*#undef CAIRO_HAS_EGL_FUNCTIONS */
/*#undef CAIRO_HAS_GLX_FUNCTIONS */
/*#undef CAIRO_HAS_QUARTZ_FONT */
/*#undef CAIRO_HAS_QUARTZ_SURFACE */
/*#undef CAIRO_HAS_WGL_FUNCTIONS */
/*#undef CAIRO_HAS_WIN32_FONT */
/*#undef CAIRO_HAS_WIN32_SURFACE */
/*#undef CAIRO_HAS_XCB_SHM_FUNCTIONS */
/*#undef CAIRO_HAS_XCB_SURFACE */
/*#undef CAIRO_HAS_XLIB_SURFACE */
/*#undef CAIRO_HAS_XLIB_XRENDER_SURFACE */

#endif
