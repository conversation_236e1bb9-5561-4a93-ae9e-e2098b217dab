.. Copyright 2018 The Cartographer Authors

.. Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

..      http://www.apache.org/licenses/LICENSE-2.0

.. Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

================
Getting involved
================

Cartographer is developed in the open and allows anyone to contribute to the project.
There are multiple ways to get involved!

If you have question or think you've found an issue in Cartographer, you are welcome to open a `GitHub issue`_.

.. _GitHub issue: https://github.com/cartographer-project/cartographer/issues

If you have an idea of a significant change that should be documented and discussed before finding its way into Cartographer, you should submit it as a pull request to `the RFCs repository`_ first.
Simpler changes can also be discussed in GitHub issues so that developers can help you get things right from the first try.

.. _the RFCs repository: https://github.com/cartographer-project/rfcs

If you want to contribute code or documentation, this is done through `GitHub pull requests`_.
Pull requests need to follow the `contribution guidelines`_.

.. _GitHub pull requests: https://github.com/cartographer-project/cartographer/pulls
.. _contribution guidelines: https://github.com/cartographer-project/cartographer/blob/master/CONTRIBUTING.md
