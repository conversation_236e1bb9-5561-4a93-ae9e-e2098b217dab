# Copyright 2018 The Cartographer Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jobs:
- job: Build
  pool:
    vmImage: 'vs2017-win2016'
  timeoutInMinutes: 360
  steps:
  - script: |
      choco sources add -n=roswin -s https://roswin.azurewebsites.net/api/v2/ --priority 1
      rem Azure VM runs out of space on C:, so use D: for ros and rosdeps
      mkdir D:\opt && mklink /J C:\opt D:\opt
      choco upgrade %ROS_METAPACKAGE% -y
      choco upgrade ros-melodic-perception -y
      robocopy "." ".\src\cartographer_ros" /E /MOVE /XD "src" > NUL
      git clone https://github.com/cartographer-project/cartographer src\cartographer
      call "C:\opt\ros\melodic\x64\env.bat" rosdep install --from-paths src --ignore-src -r -y
    env:
      ROS_METAPACKAGE: 'ros-melodic-desktop'
    displayName: Install prerequisites

  - script: |
      call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
      call "C:\opt\ros\melodic\x64\setup.bat"
      call src\cartographer\scripts\remove_mingw_cygwin_from_path.bat
      catkin_make_isolated --use-ninja --install --cmake-args -DCMAKE_BUILD_TYPE=Release
    displayName: Build

  - script: |
      call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
      call "C:\opt\ros\melodic\x64\setup.bat"
      call src\cartographer\scripts\remove_mingw_cygwin_from_path.bat
      cd build_isolated\cartographer\install && ctest --no-compress-output -T Test
    displayName: Run cartographer tests

  - script: |
      call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
      call "C:\opt\ros\melodic\x64\setup.bat"
      call src\cartographer\scripts\remove_mingw_cygwin_from_path.bat
      cd build_isolated\cartographer_ros && ninja tests && ctest --no-compress-output -T Test
    displayName: Build and run cartographer_ros tests
    condition: always()

  - script: |
      call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
      call "C:\opt\ros\melodic\x64\setup.bat"
      call src\cartographer\scripts\remove_mingw_cygwin_from_path.bat
      python src\cartographer\scripts\ctest_to_junit.py build_isolated\cartographer_ros
    displayName: Convert tests to jUnit
    condition: always()

  - task: PublishTestResults@2
    displayName: Publish test results
    inputs:
      testRunner: 'jUnit'
      testResultsFiles: '**\jUnit.xml'
      searchFolder: '$(Build.SourcesDirectory)\build_isolated\cartographer_ros\Testing'
    condition: always()
